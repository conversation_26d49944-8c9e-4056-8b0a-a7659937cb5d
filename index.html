<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Konspect — Access 9.7M+ African Businesses & Organizations</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap" rel="stylesheet">

	
	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>
/>

	<style>
		:root { --brand: #FF6039; --ink:#1D1D1D; --muted:#585D62; --paper:#FAFAFA; }
		html { scroll-behavior: smooth; }
		body { font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif; color: var(--muted); background-color: var(--paper); }
		h1, h2, h3, h4, h5, h6 { font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif; color: var(--paper); }
		.brand-font { font-family: Fredoka, system-ui, sans-serif; }
		.shadow-card { box-shadow: 0 16px 40px rgba(16,24,40,.08), 0 4px 10px rgba(16,24,40,.06); }
		.btn { transition: transform .08s ease, box-shadow .2s ease; }
		.btn:active { transform: translateY(1px); }
		/***** Slider image aspect fix on small screens *****/
		.slider-image { aspect-ratio: 16 / 10; object-fit: cover; }
	</style>
</head>

<body class="bg-white antialiased">
	<!-- Navbar -->
	<header class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 sticky top-0 z-50">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="#" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<ul class="hidden md:flex items-center gap-8 text-sm text-muted">
				<li><a href="#plans" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Pricing</a></li>
				<li><a href="#docs" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Documentation</a></li>
				<li><a href="#contact" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Contact Us</a></li>
			</ul>
			<div class="hidden md:flex items-center gap-3">
				<a href="#login" class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log In</a>
				<a href="#signup" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
			</div>
			<button id="mobileMenuBtn" class="md:hidden inline-flex h-10 w-10 items-center justify-center rounded-lg border border-black/10 text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60" aria-controls="mobileMenu" aria-expanded="false" aria-label="Open menu">
				<i class="fa-solid fa-bars"></i>
			</button>
		</nav>
		<div id="mobileMenu" class="md:hidden hidden border-t border-black/5">
			<div class="max-w-7xl mx-auto px-6 py-4 space-y-3">
				<a href="#plans" class="block">Pricing</a>
				<a href="#docs" class="block">Documentation</a>
				<a href="#contact" class="block">Contact Us</a>
				<div class="pt-3 flex gap-3">
					<a href="#login" class="btn flex-1 inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink">Log In</a>
					<a href="#signup" class="btn flex-1 inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white">Sign Up</a>
				</div>
			</div>
		</div>
	</header>

	<main>
		<!-- Hero -->
		<section class="relative bg-paper">
			<div class="max-w-7xl mx-auto px-6 py-12 md:py-16 lg:py-20 grid grid-cols-1 lg:grid-cols-12 gap-10 items-center">
				<figure class="lg:col-span-5">
					<img src="images/hero-image.jpg" alt="A hand with fingers covered in swirling, vibrant, multi-colored paint against a dark teal background." class="w-full h-[320px] sm:h-[380px] lg:h-[420px] object-cover rounded-2xl border border-black/10 shadow-card" loading="eager" />
				</figure>
				<div class="lg:col-span-7">
					<h1 class="text-4xl sm:text-5xl xl:text-6xl font-black leading-tight tracking-tight text-ink">
						Access 9.7M+<br class="hidden xl:block"> African Businesses<br class="hidden xl:block"> &amp; Organizations
					</h1>
					<p class="mt-5 max-w-2xl text-muted">
						Instantly verify businesses existence, obtain their country of registration, and tax status for all African countries under OHADA.
					</p>
					<div class="mt-7 flex flex-wrap gap-3">
						<a href="#demo" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-5 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Try a Demo</a>
						<a href="#plans" class="btn inline-flex items-center justify-center rounded-lg border border-brand/40 px-5 py-3 text-sm font-semibold text-brand hover:bg-brand/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">See our Plans</a>
					</div>
				</div>
			</div>
		</section>

		<!-- Features & Advantages Slideshow -->
		<section aria-labelledby="features-title" class="py-10 sm:py-12 lg:py-14 bg-paper">
			<h2 id="features-title" class="sr-only">Features and Advantages</h2>
			<div class="max-w-6xl mx-auto px-6 relative pb-8">
				<div class="relative bg-white border border-black/10 rounded-2xl overflow-hidden shadow-card" role="region" aria-roledescription="carousel" aria-label="Product features">
					<!-- side controls -->
					<div class="absolute inset-y-0 left-0 flex w-14 flex-col items-center justify-center border-r border-black/10 bg-white/95">
						<div class="flex flex-col gap-px h-full justify-center">
							<button id="prevSlide" class="btn h-full w-14 inline-flex items-center justify-center text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 border-b border-black/10" aria-label="Previous slide">
								
								<i class="fa-solid fa-chevron-left"></i>
							</button>
							<button id="nextSlide" class="btn h-full w-14 inline-flex items-center justify-center text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60" aria-label="Next slide">
								<i class="fa-solid fa-chevron-right"></i>
							</button>
						</div>
					</div>

					<div class="grid grid-cols-1 md:grid-cols-2">
						<article class="p-6 md:p-8 md:pl-24">
							<h3 id="slideTitle" class="font-serif text-xl sm:text-2xl font-bold text-ink">Simple &amp; Flexible Integration</h3>
							<p id="slideDesc" class="mt-3 text-sm text-muted max-w-prose">
								Requested using an easy-to-integrate URL structure, delivered in lightweight JSON format, and secured via 256-bit HTTPS encryption.
							</p>
						</article>
						<figure class="md:min-h-[260px] p-4 md:p-6">
							<img id="slideImage" class="slider-image w-full rounded-xl border border-black/10 object-cover" src="images/slider.jpg" alt="Close-up of a dark computer screen showing lines of code with syntax highlighting." loading="lazy">
						</figure>
					</div>
				</div>

				<!-- dots -->
				<div class="absolute bottom-0 left-1/2 -translate-x-1/2 flex items-center gap-2" aria-label="Slide indicators">
					<button class="dot h-2 w-2 rounded-full bg-gray-300" aria-label="Go to slide 1" aria-controls="slideTitle"></button>
					<button class="dot h-2 w-2 rounded-full bg-gray-300" aria-label="Go to slide 2" aria-controls="slideTitle"></button>
					<button class="dot h-2 w-2 rounded-full bg-gray-300" aria-label="Go to slide 3" aria-controls="slideTitle"></button>
					<button class="dot h-2 w-2 rounded-full bg-gray-300" aria-label="Go to slide 4" aria-controls="slideTitle"></button>
				</div>
			</div>
		</section>

		<!-- Plans -->
		<section id="plans" aria-labelledby="plans-title" class="py-14 sm:py-18 lg:py-24 bg-paper">
			<div class="max-w-6xl mx-auto px-6">
				<h2 id="plans-title" class="text-center font-serif text-3xl sm:text-4xl font-black text-ink">
					<span class="text-brand">Our Plans</span>
				</h2>

				<div class="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
					<!-- Subscription Plan -->
					<article class="rounded-2xl border border-brand/60 bg-white p-8 text-center shadow-card">
						<h3 class="text-xl font-serif font-bold">Subscription Plan</h3>
						<p class="mt-8 text-sm text-muted/80">From</p>
						<p class="text-4xl font-extrabold tracking-tight text-ink">$49.99<span class="text-base font-normal text-muted">/ mo</span></p>
						<p class="mt-8 text-sm text-muted/80">From</p>
						<p class="text-2xl font-bold text-ink">100 <span class="text-base font-normal text-muted">rqsts/ mo</span></p>
						<div class="mt-10">
							<a href="#signup" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
						</div>
					</article>

					<!-- Pay As You Go -->
					<article class="rounded-2xl border border-black/10 bg-white p-8 text-center shadow-card">
						<h3 class="text-xl font-serif font-bold">Pay As You Go</h3>
						<p class="mt-8 text-4xl font-extrabold tracking-tight text-ink">$1.99<span class="text-base font-normal text-muted">/ rqst</span></p>
						<p class="mt-8 text-2xl font-bold text-ink">Unlimited <span class="text-base font-normal text-muted">rqsts</span></p>
						<div class="mt-10">
							<a href="#signup" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-6 py-3 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
						</div>
					</article>
				</div>
			</div>
		</section>
	</main>

	<!-- Footer -->
	<footer class="bg-brand text-paper" id="contact">
		<div class="max-w-7xl mx-auto px-6 pt-12 pb-10">
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
				<section aria-labelledby="office-title">
					<h3 id="office-title" class="font-semibold text-paper">Head Office</h3>
					<ul class="mt-4 space-y-1 text-sm/6 text-white/90">
						<li>Sirius House</li>
						<li>Delta Crescent</li>
						<li>Warrington</li>
						<li>WA5 7NS</li>
						<li>United Kingdom</li>
						<li>01234567890</li>
					</ul>
				</section>

				<section aria-labelledby="policies-title">
					<h3 id="policies-title" class="font-semibold text-paper">Policies and Documentation</h3>
					<ul class="mt-4 grid grid-cols-2 gap-x-6 gap-y-1 text-sm/6 text-white/90">
						
						<li><a href="#" class="hover:underline">Cookie policy</a></li>
						<li><a href="#" class="hover:underline">Information Security</a></li>
						<li><a href="#" class="hover:underline">Modern Slavery Act</a></li>
						<li><a href="#" class="hover:underline">Privacy Policy</a></li>
						<li><a href="#" class="hover:underline">Quality Policy</a></li>
						<li><a href="#" class="hover:underline">Terms and Conditions</a></li>
					</ul>
				</section>

				<section aria-labelledby="social-title">
					<h3 id="social-title" class="font-semibold text-paper">Social Media</h3>
					<div class="mt-4 flex items-center gap-4 text-lg">
						<a aria-label="Facebook" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-facebook-f"></i></a>
						<a aria-label="Instagram" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-instagram"></i></a>
						<a aria-label="WhatsApp" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-whatsapp"></i></a>
						<a aria-label="YouTube" href="#" class="btn inline-flex h-9 w-9 items-center justify-center rounded-full bg-white/10 hover:bg-white/20"><i class="fab fa-youtube"></i></a>
					</div>
				</section>

				<section aria-labelledby="newsletter-title">
					<h3 id="newsletter-title" class="font-semibold text-paper">Subscribe to Newsletter</h3>
					<form class="mt-4 flex gap-3" action="#" method="get">
						<label for="newsletter-email" class="sr-only">Email Address</label>
						<input id="newsletter-email" type="email" required placeholder="Email Address" class="w-full rounded-lg border-0 px-4 py-2.5 text-ink placeholder:text-muted/70 focus:ring-2 focus:ring-brand/60">
						<button type="submit" class="btn rounded-lg bg-white px-4 py-2.5 text-sm font-semibold text-brand hover:bg-white/90">Subscribe</button>
					</form>
				</section>
			</div>

			<div class="mt-12 border-t border-white/20 pt-6 text-xs text-white/80">
				<p>&copy; 2023 Konspect Sarl. Registered in Togo, NIF: XXXXXXXX, RCCM: </p>
			</div>
		</div>
	</footer>

	<!-- Scripts: tiny interactivity only -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js" ></script>
	<script>
		// Mobile menu toggle
		(function(){
			const btn = document.getElementById('mobileMenuBtn');
			const menu = document.getElementById('mobileMenu');
			if(!btn || !menu) return;
			btn.addEventListener('click', () => {
				const expanded = btn.getAttribute('aria-expanded') === 'true';
				btn.setAttribute('aria-expanded', String(!expanded));
				menu.classList.toggle('hidden');
			});
		})();

		// Features slideshow
		(function(){
			const slides = [
				{
					title: 'Simple & Flexible Integration',
					desc: 'Requested using an easy-to-integrate URL structure, delivered in lightweight JSON format, and secured via 256-bit HTTPS encryption.',
					img: 'images/slider.jpg',
					alt: 'Close-up of a dark computer screen showing lines of code with syntax highlighting.'
				},
				{
					title: 'Pan‑African Coverage',
					desc: 'Verify companies across all OHADA jurisdictions with a single API. Real‑time lookups with consistent fields.',
					img: 'images/slider.jpg',
					alt: 'Developers collaborating'
				},
				{
					title: 'Bank‑grade Security',
					desc: 'All traffic is encrypted in transit with TLS 1.2+ and signed responses. Role based access and audit logs included.',
					img: 'images/slider.jpg',
					alt: 'Secure lock'
				},
				{
					title: 'Transparent Pricing',
					desc: 'Simple subscription or pay‑as‑you‑go. No hidden fees. Bulk discounts available for high‑volume partners.',
					img: 'images/slider.jpg',
					alt: 'Pricing planning'
				}
			];

			const titleEl = document.getElementById('slideTitle');
			const descEl = document.getElementById('slideDesc');
			const imgEl = document.getElementById('slideImage');
			const prevBtn = document.getElementById('prevSlide');
			const nextBtn = document.getElementById('nextSlide');
			const dots = Array.from(document.querySelectorAll('.dot'));

			let index = 0; let autoplay; const AUTOPLAY_MS = 7000; let paused = false;

			function update(activeIndex){
				const s = slides[activeIndex];
				titleEl.textContent = s.title;
				descEl.textContent = s.desc;
				imgEl.src = s.img;
				imgEl.alt = s.alt;
				dots.forEach((d,i)=>{
					d.classList.toggle('bg-brand', i===activeIndex);
					d.classList.toggle('bg-gray-300', i!==activeIndex);
				});
			}

			function next(){ index = (index + 1) % slides.length; update(index); }
			function prev(){ index = (index - 1 + slides.length) % slides.length; update(index); }

			function start(){ autoplay = setInterval(()=>{ if(!paused) next(); }, AUTOPLAY_MS); }
			function stop(){ clearInterval(autoplay); }

			prevBtn.addEventListener('click', prev);
			nextBtn.addEventListener('click', next);
			dots.forEach((d,i)=> d.addEventListener('click', ()=>{ index=i; update(index); }));

			// Pause on hover
			const region = prevBtn.closest('[role="region"]');
			region.addEventListener('mouseenter', ()=>{ paused=true; });
			region.addEventListener('mouseleave', ()=>{ paused=false; });

			// Keyboard support
			region.addEventListener('keydown', (e)=>{
				if(e.key==='ArrowRight') { next(); }
				if(e.key==='ArrowLeft') { prev(); }
			});

			update(index); start();
			window.addEventListener('visibilitychange', ()=>{ document.hidden ? stop() : start(); });
		})();
	</script>
</body>
</html>