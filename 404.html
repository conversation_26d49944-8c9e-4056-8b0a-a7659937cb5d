<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>404 Error — Konspect</title>

	<!-- Google Fonts: <PERSON><PERSON> (brand), <PERSON><PERSON> (body), <PERSON><PERSON><PERSON><PERSON> (headings) -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@600;700&family=Lato:wght@300;400;700;900&family=Merriweather:wght@700;900&display=swap" rel="stylesheet">

	<!-- Font Awesome (for icons) -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">

	<!-- Tailwind CSS CDN -->
	<script src="https://cdn.tailwindcss.com"></script>
	<script>
		tailwind.config = {
			theme: {
				extend: {
					colors: {
						brand: '#FF6039',
						ink: '#1D1D1D',
						muted: '#585D62',
						paper: '#FAFAFA'
					},
					fontFamily: {
						sans: ['Lato', 'ui-sans-serif', 'system-ui', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', 'Noto Sans', 'sans-serif'],
						serif: ['Merriweather', 'Georgia', 'Cambria', 'Times New Roman', 'Times', 'serif'],
						fredoka: ['Fredoka', 'system-ui', 'sans-serif']
					}
				}
			}
		}
	</script>

	<style>
		:root { --brand: #FF6039; --ink:#1D1D1D; --muted:#585D62; --paper:#FAFAFA; }
		html { scroll-behavior: smooth; }
		body { font-family: Lato, ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica Neue, Arial, Noto Sans, sans-serif; color: var(--muted); background-color: var(--paper); }
		h1, h2, h3, h4, h5, h6 { font-family: Merriweather, Georgia, Cambria, Times New Roman, Times, serif; color: var(--paper); }
		.brand-font { font-family: Fredoka, system-ui, sans-serif; }
		.shadow-card { box-shadow: 0 16px 40px rgba(16,24,40,.08), 0 4px 10px rgba(16,24,40,.06); }
		.btn { transition: transform .08s ease, box-shadow .2s ease; }
		.btn:active { transform: translateY(1px); }

		/* 404 Error Styling */
		.error-display {
			font-family: 'Merriweather', serif;
			font-weight: 900;
			line-height: 0.8;
			letter-spacing: -0.02em;
		}
		
		.error-four-top {
			font-size: clamp(8rem, 20vw, 16rem);
			color: var(--brand);
			margin-left: 1em;
		}
		
		.error-text {
			font-size: clamp(6rem, 15vw, 12rem);
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 0;
		}
		
		.error-four-bottom {
			font-size: clamp(8rem, 20vw, 16rem);
			color: var(--brand);
			margin-left: 1em;
		}
		
		.letter-e1 { color: var(--ink); }
		.letter-r1 { color: var(--ink); }
		.letter-r2 { color: var(--brand); }
		.letter-o { color: var(--ink); }
		.letter-r3 { color: var(--brand); }
	</style>
</head>

<body class="bg-white antialiased">
	<!-- Navbar -->
	<header class="border-b border-black/5 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70 sticky top-0 z-50">
		<nav class="max-w-7xl mx-auto px-6 py-4 flex items-center justify-between" aria-label="Primary">
			<a href="/" class="flex items-center gap-2 group" aria-label="Konspect home">
				<span class="brand-font text-2xl font-bold tracking-tight text-brand">Konspect</span>
			</a>
			<ul class="hidden md:flex items-center gap-8 text-sm text-muted">
				<li><a href="#plans" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Pricing</a></li>
				<li><a href="#docs" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Documentation</a></li>
				<li><a href="#contact" class="hover:text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60 rounded px-1">Contact Us</a></li>
			</ul>
			<div class="hidden md:flex items-center gap-3">
				<a href="#login" class="btn inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink hover:bg-black/5 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Log In</a>
				<a href="#signup" class="btn inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60">Sign Up</a>
			</div>
			<button id="mobileMenuBtn" class="md:hidden inline-flex h-10 w-10 items-center justify-center rounded-lg border border-black/10 text-ink focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-brand/60" aria-controls="mobileMenu" aria-expanded="false" aria-label="Open menu">
				<i class="fa-solid fa-bars"></i>
			</button>
		</nav>
		<div id="mobileMenu" class="md:hidden hidden border-t border-black/5">
			<div class="max-w-7xl mx-auto px-6 py-4 space-y-3">
				<a href="#plans" class="block">Pricing</a>
				<a href="#docs" class="block">Documentation</a>
				<a href="#contact" class="block">Contact Us</a>
				<div class="pt-3 flex gap-3">
					<a href="#login" class="btn flex-1 inline-flex items-center justify-center rounded-lg border border-black/10 bg-white px-4 py-2 text-sm font-semibold text-ink">Log In</a>
					<a href="#signup" class="btn flex-1 inline-flex items-center justify-center rounded-lg bg-brand px-4 py-2 text-sm font-semibold text-white">Sign Up</a>
				</div>
			</div>
		</div>
	</header>

	<main class="flex-1 min-h-screen bg-paper">
		<!-- 404 Error Display -->
		<section class="flex flex-col items-center justify-center min-h-[calc(100vh-80px)] px-6">
			<div class="error-display text-center select-none">
				<!-- Top 4 -->
				<div class="error-four-top">4</div>
				
				<!-- ERROR text -->
				<div class="error-text mt-4 mb-4">
					<span class="letter-e1">E</span>
					<span class="letter-r1">R</span>
					<span class="letter-r2">R</span>
					<span class="letter-o">O</span>
					<span class="letter-r3">R</span>
				</div>
				
				<!-- Bottom 4 -->
				<div class="error-four-bottom">4</div>
			</div>
		</section>
	</main>

	<!-- Scripts: tiny interactivity only -->
	<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/js/all.min.js"></script>
	<script>
		// Mobile menu toggle
		(function(){
			const btn = document.getElementById('mobileMenuBtn');
			const menu = document.getElementById('mobileMenu');
			if(!btn || !menu) return;
			btn.addEventListener('click', () => {
				const expanded = btn.getAttribute('aria-expanded') === 'true';
				btn.setAttribute('aria-expanded', String(!expanded));
				menu.classList.toggle('hidden');
			});
		})();
	</script>
</body>
</html>